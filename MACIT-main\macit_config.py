#!/usr/bin/env python
# -*- coding: utf-8 -*-

# ========= Copyright 2023-2024 @ CAMEL-AI.org. All Rights Reserved. =========
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
# ========= Copyright 2023-2024 @ CAMEL-AI.org. All Rights Reserved. =========

"""
MACIT (Multi-Agent Collaborative Intent Tagging) 框架配置文件
"""

import os
from pathlib import Path

# 基本路径配置
BASE_DIR = Path(__file__).parent
DATA_DIR = BASE_DIR / "data"
CUT_DIR = DATA_DIR / "cut"
INTERA_DATA_DIR = CUT_DIR / "intera_data"
USER_PROFILES_DIR = CUT_DIR / "processed_user_profiles"
OUTPUT_DIR = BASE_DIR / "output"
LOGS_DIR = BASE_DIR / "logs"

# 确保输出和日志目录存在
os.makedirs(OUTPUT_DIR, exist_ok=True)
os.makedirs(LOGS_DIR, exist_ok=True)

# 模型配置
MODEL_CONFIG = {
    "api_base": "https://api.deepseek.com/v1",
    "model_name": "deepseek-chat",
    "api_key": "***********************************",
    "temperature": 0.5,
    "max_tokens": 2048,
    "top_p": 0.9,
}

# 文件路径配置
USER_PROFILES_PATH = USER_PROFILES_DIR / "real_cut_user_profiles_500_fixed.json"
CUT_DESCRIPTION_PATH = CUT_DIR / "cut_description.txt"

# 任务配置
TASK_CONFIG = {
    "max_debate_rounds": 3,  # 最大辩论轮数
    "similarity_threshold": 0.7,  # 意图相似度阈值，超过此值认为达成共识
    "intent_categories": {
        "expressive": [
            "Commenting",
            "Writing Articles",
            "Joining Discussions"
        ],
        "active": [
            "Organizing Events",
            "Advocating Actions",
            "Voting"
        ],
        "observant": [
            "Observing",
            "Recording",
            "Remaining Silent"
        ],
        "resistant": [
            "Opposing",
            "Arguing",
            "Protesting"
        ]
    }
}

# 提示模板配置
PROMPT_TEMPLATES = {
    # 智能体1的系统提示
    "agent1_system": """你是一位专业的社交媒体用户意图分析专家，擅长从用户的交互行为中分析其深层次的动机和意图。
你需要基于用户的画像信息、交互上下文和行为，深入分析用户的真实意图。
你的分析应该客观、全面、具有洞察力，并且要考虑到社交媒体环境的特殊性。""",

    # 智能体2的系统提示
    "agent2_system": """你是一位资深的社交媒体心理学家，专注于理解用户在社交媒体平台上的行为动机和意图。
你需要通过分析用户的画像、交互上下文和具体行为，揭示其背后的心理动机和真实意图。
你的分析应该深入、细致，并且要考虑到用户的个人特点和社会背景。""",

    # 辩论提示模板
    "debate_template": """# 任务描述
你需要与另一位分析专家一起，讨论并确定用户在特定社交媒体交互中的真实意图。你们需要通过辩论和讨论，达成关于用户意图的共识。

# 用户画像
{user_profile}

# 交互样本
上下文内容: {context_text}
上下文作者: {context_author}
用户行为类型: {action_type}
用户发布内容: {action_text}

# 辩论要求
1. 基于提供的用户画像和交互信息，分析用户的真实意图
2. 考虑用户的认知特征、情感状态和立场
3. 参考元意图字典，但不限于这些类别
4. 辩论应该聚焦于用户的动机、意图和行为的一致性分析

# 当前辩论轮次
轮次: {round_num}

# 之前的讨论内容
{previous_discussion}

# 对方的意见
{other_opinion}

# 输出形式
请以JSON格式输出你的分析结果，包括以下字段：
1. 思考过程：详细分析用户的画像特征、交互上下文和行为表现
2. 意图分析：包括动机、意图和行为三个方面
3. 对对方意见的评价：分析对方观点的合理性和不足之处
4. 是否接受对方意见：true或false
5. 最终意图标签：如果接受对方意见，给出综合后的意图标签；如果不接受，给出自己的意图标签

<思考>
[在这里详细分析用户画像、交互上下文和行为]
</思考>

{{
  "thought_process": "我的详细分析过程...",
  "intent_analysis": {{
    "motivation": "用户的内在动机是...",
    "intention": "用户的具体意图是...",
    "behavior": "用户的行为表现为..."
  }},
  "evaluation_of_other_opinion": "对对方观点的评价...",
  "accept_other_opinion": true/false,
  "final_intent_label": {{
    "motivation": "最终确定的动机...",
    "intention": "最终确定的意图...",
    "behavior": "最终确定的行为..."
  }}
}}"""
}