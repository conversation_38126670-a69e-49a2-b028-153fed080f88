#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
使用DeepSeek API测试MACIT框架的脚本
从数据集中选择10个用户样本进行意图分析
"""

import json
import logging
import random
from pathlib import Path
from typing import List, Dict, Any

from logger_config import setup_logger
from macit_framework import MACITFramework
from data_utils import get_all_user_ids, load_interaction_data, get_interaction_sample, find_user_profile_by_id, load_user_profiles

# 设置日志
logger = setup_logger('test_deepseek')


def select_test_users(num_users: int = 10) -> List[str]:
    """
    从数据集中随机选择测试用户
    
    Args:
        num_users: 要选择的用户数量
        
    Returns:
        用户ID列表
    """
    all_user_ids = get_all_user_ids()
    logger.info(f"总共找到 {len(all_user_ids)} 个用户")
    
    # 随机选择用户
    selected_users = random.sample(all_user_ids, min(num_users, len(all_user_ids)))
    logger.info(f"选择了 {len(selected_users)} 个用户进行测试: {selected_users}")
    
    return selected_users


def format_analysis_result(user_id: str, interaction_sample: Dict, analysis_result: Dict, user_profile: Dict = None) -> Dict:
    """
    格式化分析结果为指定格式
    
    Args:
        user_id: 用户ID
        interaction_sample: 交互样本
        analysis_result: 分析结果
        user_profile: 用户画像
        
    Returns:
        格式化后的结果
    """
    # 提取上下文
    context = interaction_sample.get("context", {})
    context_text = context.get("text", "")
    context_author = context.get("author_username", "") or context.get("author_id", "")
    
    # 提取用户回答
    user_response = interaction_sample.get("text", "")
    action_type = interaction_sample.get("type", "")
    
    # 提取分析出的意图
    final_intent = analysis_result.get("final_intent", {})
    motivation = final_intent.get("motivation", "")
    intention = final_intent.get("intention", "")
    behavior = final_intent.get("behavior", "")
    
    # 格式化结果
    formatted_result = {
        "用户ID": user_id,
        "用户名": user_profile.get("username", "") if user_profile else "",
        "上下文": {
            "内容": context_text,
            "作者": context_author
        },
        "用户回答": {
            "类型": action_type,
            "内容": user_response
        },
        "意图分析": {
            "动机": motivation,
            "意图": intention,
            "行为": behavior
        },
        "分析详情": {
            "辩论轮数": analysis_result.get("debate_rounds", 0),
            "是否达成共识": analysis_result.get("consensus_reached", False),
            "交互索引": analysis_result.get("interaction_index", 0)
        }
    }
    
    return formatted_result


def main():
    """主函数"""
    logger.info("开始DeepSeek API测试...")
    
    try:
        # 创建MACIT框架
        logger.info("正在初始化MACIT框架...")
        framework = MACITFramework()
        logger.info("MACIT框架初始化完成")
        
        # 加载用户画像
        user_profiles = load_user_profiles()
        
        # 选择测试用户
        test_users = select_test_users(10)
        
        # 存储所有结果
        all_results = []
        
        # 对每个用户进行分析
        for i, user_id in enumerate(test_users, 1):
            logger.info(f"正在分析用户 {i}/{len(test_users)}: {user_id}")
            
            try:
                # 加载用户交互数据
                interaction_data = load_interaction_data(user_id)
                interaction_sample = get_interaction_sample(interaction_data, 0)  # 使用第一个交互
                
                # 查找用户画像
                user_profile = find_user_profile_by_id(user_id, user_profiles)
                
                # 打印交互信息
                logger.info(f"用户交互信息:")
                logger.info(f"  交互类型: {interaction_sample.get('type', '')}")
                logger.info(f"  交互内容: {interaction_sample.get('text', '')[:100]}...")
                logger.info(f"  上下文内容: {interaction_sample.get('context', {}).get('text', '')[:100]}...")
                
                # 分析用户意图
                logger.info(f"开始分析用户 {user_id} 的意图...")
                analysis_result = framework.analyze_user_intent(
                    user_id=user_id,
                    interaction_index=0,
                    max_rounds=2,  # 限制为2轮以节省时间
                )
                
                # 格式化结果
                formatted_result = format_analysis_result(
                    user_id=user_id,
                    interaction_sample=interaction_sample,
                    analysis_result=analysis_result,
                    user_profile=user_profile
                )
                
                all_results.append(formatted_result)
                
                # 保存单个结果
                framework.save_analysis_result(
                    result=analysis_result,
                    user_id=user_id,
                    interaction_index=0
                )
                
                logger.info(f"用户 {user_id} 分析完成")
                logger.info(f"  辩论轮数: {analysis_result.get('debate_rounds', 0)}")
                logger.info(f"  是否达成共识: {analysis_result.get('consensus_reached', False)}")
                
                final_intent = analysis_result.get('final_intent', {})
                logger.info(f"  最终意图:")
                logger.info(f"    动机: {final_intent.get('motivation', '')}")
                logger.info(f"    意图: {final_intent.get('intention', '')}")
                logger.info(f"    行为: {final_intent.get('behavior', '')}")
                
            except Exception as e:
                logger.error(f"分析用户 {user_id} 时出错: {e}")
                continue
        
        # 保存汇总结果
        output_file = Path("MACIT-main/output/deepseek_test_results.json")
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(all_results, f, ensure_ascii=False, indent=2)
        
        logger.info(f"所有分析完成！共分析了 {len(all_results)} 个用户")
        logger.info(f"结果已保存至: {output_file}")
        
        # 打印汇总信息
        print("\n" + "="*80)
        print("DeepSeek API 测试结果汇总")
        print("="*80)
        
        for i, result in enumerate(all_results, 1):
            print(f"\n{i}. 用户: {result['用户ID']} ({result['用户名']})")
            print(f"   上下文: {result['上下文']['内容'][:100]}...")
            print(f"   用户回答: {result['用户回答']['内容'][:100]}...")
            print(f"   意图分析:")
            print(f"     动机: {result['意图分析']['动机']}")
            print(f"     意图: {result['意图分析']['意图']}")
            print(f"     行为: {result['意图分析']['行为']}")
            print(f"   分析详情: {result['分析详情']['辩论轮数']}轮辩论, 共识: {result['分析详情']['是否达成共识']}")
        
    except Exception as e:
        logger.error(f"测试过程中出现错误: {e}")
        raise


if __name__ == "__main__":
    main()
