#!/usr/bin/env python
# -*- coding: utf-8 -*-

# ========= Copyright 2023-2024 @ CAMEL-AI.org. All Rights Reserved. =========
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
# ========= Copyright 2023-2024 @ CAMEL-AI.org. All Rights Reserved. =========

"""
模型接口模块，用于与本地部署的Llama 3.1-8b模型交互
"""

import json
import logging
import os
import re
import requests
from typing import Dict, Any, Optional, List, Tuple

from camel.models import ModelFactory
from camel.types import ModelPlatformType, ModelType
from camel.agents import ChatAgent
from camel.messages import BaseMessage
from camel.models.stub_model import StubModel

from macit_config import MODEL_CONFIG

# 配置日志
logger = logging.getLogger(__name__)


def create_custom_model():
    """
    创建自定义模型

    Returns:
        自定义模型实例
    """

    # 创建自定义模型配置
    model_config = {
            "temperature": MODEL_CONFIG["temperature"],
            "max_tokens": MODEL_CONFIG["max_tokens"],
            "top_p": MODEL_CONFIG["top_p"],
    }

    logger.info(f"使用DeepSeek平台创建模型，名称: {MODEL_CONFIG['model_name']}, URL: {MODEL_CONFIG['api_base']}")
    logger.info(f"模型配置: {model_config}")

    model = ModelFactory.create(
        model_platform=ModelPlatformType.OPENAI_COMPATIBLE_MODEL,
        model_type=MODEL_CONFIG["model_name"],
        url=MODEL_CONFIG["api_base"],
        api_key=MODEL_CONFIG["api_key"]
    )

    logger.info(f"成功创建自定义模型: {MODEL_CONFIG['model_name']} 使用DeepSeek平台，URL: {MODEL_CONFIG['api_base']}")
    return model


def create_agent(system_message: str, agent_id: str) -> ChatAgent:
    """
    创建智能体

    Args:
        system_message: 系统消息
        agent_id: 智能体ID

    Returns:
        ChatAgent实例
    """
    model = create_custom_model()

    # 创建系统消息
    system_msg = BaseMessage.make_assistant_message(
        role_name="Assistant",
        content=system_message,
    )

    try:
        # 尝试使用agent_id参数创建智能体
        agent = ChatAgent(
            system_message=system_msg,
            model=model,
            agent_id=agent_id,
        )
    except TypeError:
        # 如果不支持agent_id参数，则不使用该参数
        logger.warning("ChatAgent不支持agent_id参数，使用默认ID")
        agent = ChatAgent(
            system_message=system_msg,
            model=model,
        )

    logger.info(f"成功创建智能体: {agent_id}")
    return agent


def parse_response(response_text: str) -> Tuple[str, Dict]:
    """
    解析模型响应，提取思考过程和JSON格式的结果

    Args:
        response_text: 模型响应文本

    Returns:
        思考过程和解析后的JSON对象
    """
    # 提取思考过程
    thought_match = re.search(r'<思考>(.*?)</思考>', response_text, re.DOTALL)
    thought_process = thought_match.group(1).strip() if thought_match else ""

    # 提取JSON部分
    json_pattern = r'({[\s\S]*})'
    json_match = re.search(json_pattern, response_text)

    if not json_match:
        logger.error(f"无法从响应中提取JSON: {response_text}")
        return thought_process, {}

    json_str = json_match.group(1)

    try:
        result = json.loads(json_str)
        logger.info("成功解析JSON响应")
        return thought_process, result
    except json.JSONDecodeError as e:
        logger.error(f"JSON解析错误: {e}, 原始JSON: {json_str}")
        # 尝试修复常见的JSON格式问题
        try:
            # 替换单引号为双引号
            json_str = json_str.replace("'", '"')
            # 替换True/False为true/false
            json_str = json_str.replace("True", "true").replace("False", "false")
            result = json.loads(json_str)
            logger.info("修复后成功解析JSON响应")
            return thought_process, result
        except json.JSONDecodeError:
            logger.error("修复后仍然无法解析JSON")
            return thought_process, {}


def calculate_similarity(intent1: Dict, intent2: Dict) -> float:
    """
    计算两个意图之间的相似度

    Args:
        intent1: 第一个意图
        intent2: 第二个意图

    Returns:
        相似度得分 (0-1)
    """
    if not intent1 or not intent2:
        return 0.0

    # 提取关键字段
    fields = ["motivation", "intention", "behavior"]

    # 计算每个字段的相似度
    similarities = []
    for field in fields:
        text1 = intent1.get(field, "").lower()
        text2 = intent2.get(field, "").lower()

        if not text1 or not text2:
            similarities.append(0.0)
            continue

        # 简单的文本相似度计算 (可以替换为更复杂的算法)
        # 这里使用词集合的Jaccard相似度
        words1 = set(text1.split())
        words2 = set(text2.split())

        if not words1 or not words2:
            similarities.append(0.0)
            continue

        intersection = len(words1.intersection(words2))
        union = len(words1.union(words2))

        if union == 0:
            similarities.append(0.0)
        else:
            similarities.append(intersection / union)

    # 返回平均相似度
    if not similarities:
        return 0.0

    return sum(similarities) / len(similarities)